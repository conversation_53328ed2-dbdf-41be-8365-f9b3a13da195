# update && upgrade if needed
# sudo apt update && sudo apt upgrade -y


# create user if needed
# sudo useradd coloraria
# sudo usermod -aG sudo coloraria
# sudo usermod -aG docker coloraria

# pulling secret - todo
# pushing secret for now
# sudo scp -i ~/.ssh/worker -r letsencrypt scripts traefik systemctl coloraria@<server_ip>:line-art-generator-v2/
# scp -i ~/.ssh/worker .env .env.production coloraria@<server_ip>:line-art-generator-v2/

# setup monitoring service
# pull systemctl service definition - todo
# repo is not there yet at this point - need to pull repo first
# create log directory if needed - split files by days, easier to remove old logs
# cp /root/line-art-generator-v2/systemctl/posthog-monitor.service /etc/systemd/system/posthog-monitor.service
# sudo systemctl daemon-reload
# sudo systemctl enable posthog-monitor.service
# sudo systemctl start posthog-monitor.service

# setting docker env
docker network create traefik-net



