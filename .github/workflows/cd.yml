name: Continuous Deployment

on:
  push:
    branches: [ "master" ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: webfactory/ssh-agent@v0.4.1
      with:
        ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}
    - run: mkdir -p ~/.ssh/ && ssh-keyscan -H ${{ secrets.SERVER_IPV4 }} >> ~/.ssh/known_hosts
    - run: ssh ${{ vars.WORKER }}@${{ secrets.SERVER_IPV4 }} line-art-generator-v2/scripts/run.sh
